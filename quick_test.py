#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 专门测试商品ID提取
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def quick_test():
    """快速测试商品ID提取"""
    print("快速测试商品ID提取")
    print("=" * 30)
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    # 启动浏览器
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # 访问画廊列表页面
        driver.get("https://yx.mail.netease.com/distribution-operation/admin/#/gallery/list")
        
        # 简单登录处理
        time.sleep(3)
        if "login" in driver.current_url.lower():
            try:
                username_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text']")))
                username_input.send_keys("zhangxin03")
                
                password_input = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
                password_input.send_keys("XINxinzhang124#")
                
                login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
                login_button.click()
                time.sleep(5)
            except:
                input("请手动登录后按回车...")
        
        # 点击第一个tab
        tabs = driver.find_elements(By.CSS_SELECTOR, ".ant-tabs-tab")
        if tabs:
            tabs[0].click()
            time.sleep(3)
        
        # 尝试多种方式提取商品ID
        print("尝试提取商品ID...")
        
        # 方法1: 查找所有td元素
        all_tds = driver.find_elements(By.CSS_SELECTOR, "td")
        print(f"找到 {len(all_tds)} 个td元素")
        
        potential_ids = []
        for td in all_tds:
            text = td.text.strip()
            if text and 5 < len(text) < 50:
                # 检查是否像商品ID
                if any(char.isdigit() for char in text) and not text.isdigit():
                    potential_ids.append(text)
                elif text.isdigit() and len(text) > 8:
                    potential_ids.append(text)
        
        print(f"方法1找到 {len(potential_ids)} 个潜在ID:")
        for i, pid in enumerate(potential_ids[:10]):
            print(f"  {i+1}: {pid}")
        
        # 方法2: 查找特定列
        rows = driver.find_elements(By.CSS_SELECTOR, "tbody tr")
        print(f"\n找到 {len(rows)} 行数据")
        
        if rows:
            print("每行的所有列数据:")
            for i, row in enumerate(rows[:3]):  # 只看前3行
                cells = row.find_elements(By.CSS_SELECTOR, "td")
                print(f"行 {i+1}:")
                for j, cell in enumerate(cells):
                    text = cell.text.strip()
                    if text:
                        print(f"  列 {j+1}: {text}")
        
        # 方法3: 查找包含特定文本的元素
        print("\n方法3: 查找包含'YX'或长数字的元素")
        all_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'YX') or string-length(text()) > 10]")
        for i, elem in enumerate(all_elements[:10]):
            text = elem.text.strip()
            if text:
                print(f"  {i+1}: {text}")
        
        input("\n按回车关闭...")
        
    except Exception as e:
        print(f"错误: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    quick_test()
