#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 包含页面元素选择器和其他配置项
可以根据实际页面结构调整这些选择器
"""

# 页面URL配置
GALLERY_URL = "https://yx.mail.netease.com/distribution-operation/admin/#/gallery/list"
PICTURE_URL = "https://yx.mail.netease.com/distribution-operation/admin/#/picture/primary/list"

# 等待时间配置（秒）
PAGE_LOAD_WAIT = 3
SEARCH_WAIT = 3
ELEMENT_WAIT = 10
REQUEST_DELAY = 1

# Tab相关选择器
TAB_SELECTORS = [
    ".ant-tabs-tab",
    ".tab-item",
    "[role='tab']",
    ".tabs .tab"
]

# 商品ID相关选择器
PRODUCT_ID_SELECTORS = [
    "td[data-label*='商品ID']",
    "td[data-label*='渠道商品ID']", 
    ".product-id",
    "[class*='product-id']",
    "td:nth-child(2)",  # 假设商品ID在第二列
    "td:nth-child(3)",  # 假设商品ID在第三列
]

# 商品ID XPath选择器
PRODUCT_ID_XPATH_SELECTORS = [
    "//td[contains(text(), 'YX') or contains(text(), 'yx')]",
    "//td[string-length(text()) > 10 and string-length(text()) < 50]",
    "//span[contains(@class, 'product') or contains(@class, 'id')]",
]

# 搜索框选择器
SEARCH_INPUT_SELECTORS = [
    "input[placeholder*='搜索']",
    "input[placeholder*='商品']",
    "input[placeholder*='ID']",
    ".ant-input",
    ".search-input",
    "input[type='text']"
]

# 搜索按钮选择器
SEARCH_BUTTON_SELECTORS = [
    "button[type='submit']",
    ".ant-btn-primary",
    "button:contains('搜索')",
    ".search-btn",
    ".btn-search"
]

# 无数据提示选择器
NO_DATA_SELECTORS = [
    ".ant-empty",
    ".no-data",
    "[class*='empty']",
    "[class*='no-data']",
    ".empty-state"
]

# 无数据提示文本
NO_DATA_TEXTS = [
    "暂无",
    "无数据", 
    "empty",
    "没有找到",
    "no data",
    "暂无数据"
]

# 分页相关选择器
NEXT_PAGE_SELECTORS = [
    ".ant-pagination-next:not(.ant-pagination-disabled)",
    ".pagination-next:not(.disabled)",
    ".next-page:not(.disabled)"
]

# 表格数据行选择器
TABLE_ROW_SELECTORS = [
    ".ant-table-tbody tr:not(.ant-table-placeholder)",
    ".table-body tr",
    "tbody tr:not(.empty-row)"
]

# Chrome浏览器选项
CHROME_OPTIONS = [
    '--no-sandbox',
    '--disable-dev-shm-usage', 
    '--disable-gpu',
    '--window-size=1920,1080',
    '--disable-blink-features=AutomationControlled'
]

# 商品ID验证规则
def is_valid_product_id(text):
    """验证是否为有效的商品ID"""
    if not text or not isinstance(text, str):
        return False
    
    text = text.strip()
    
    # 长度检查
    if len(text) < 5 or len(text) > 50:
        return False
    
    # 包含特定前缀
    if text.startswith(('YX', 'yx', 'Yx')):
        return True
    
    # 纯数字且长度合适
    if text.isdigit() and 8 <= len(text) <= 20:
        return True
    
    # 字母数字组合
    if text.isalnum() and 10 <= len(text) <= 30:
        return True
    
    return False

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'yanxuan_scraper.log'
}
