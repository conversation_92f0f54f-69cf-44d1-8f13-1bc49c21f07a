# 严选运营后台商品ID抓取工具使用指南

## 快速开始

### 1. 环境准备
确保您的系统已安装：
- Python 3.7+
- Chrome浏览器

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 测试环境
```bash
python test_scraper.py
```

### 4. 演示功能
```bash
python demo_scraper.py
```
这个演示脚本会打开浏览器，让您手动登录并查看页面结构。

### 5. 运行完整抓取
```bash
python yanxuan_scraper.py
```

## 详细说明

### 脚本功能
1. **yanxuan_scraper.py** - 主要的抓取脚本
2. **test_scraper.py** - 环境测试脚本
3. **demo_scraper.py** - 演示脚本，用于调试页面结构
4. **config.py** - 配置文件，包含各种选择器和设置

### 工作流程
1. 访问画廊列表页面
2. 获取所有tab标签
3. 逐个点击tab，抓取每个tab下的商品ID
4. 支持翻页，抓取所有页面的数据
5. 在主图列表页面搜索每个商品ID
6. 记录搜索无结果的商品ID和对应tab名称
7. 保存结果到CSV和JSON文件

### 输出文件
- `not_found_products_YYYYMMDD_HHMMSS.csv` - 未找到的商品ID列表（CSV格式）
- `not_found_products_YYYYMMDD_HHMMSS.json` - 未找到的商品ID列表（JSON格式）
- `yanxuan_scraper.log` - 详细的运行日志

### 配置调整
如果页面结构发生变化，您可以修改 `config.py` 文件中的选择器：

```python
# Tab相关选择器
TAB_SELECTORS = [
    ".ant-tabs-tab",
    ".tab-item",
    # 添加新的选择器
]

# 商品ID相关选择器
PRODUCT_ID_SELECTORS = [
    "td[data-label*='商品ID']",
    # 添加新的选择器
]
```

### 常见问题

#### 1. 登录问题
- 脚本会自动检测是否需要登录
- 如果需要登录，会提示您手动登录
- 建议首次运行使用有头模式（选择 'n'）

#### 2. 元素定位失败
- 运行 `demo_scraper.py` 查看页面结构
- 根据实际页面调整 `config.py` 中的选择器
- 查看日志文件了解详细错误信息

#### 3. 网络超时
- 检查网络连接
- 可以在 `config.py` 中增加等待时间

#### 4. Chrome驱动问题
- 脚本会自动下载匹配的Chrome驱动
- 确保Chrome浏览器是最新版本

### 高级用法

#### 自定义商品ID验证规则
在 `config.py` 中修改 `is_valid_product_id` 函数：

```python
def is_valid_product_id(text):
    """自定义商品ID验证逻辑"""
    # 添加您的验证规则
    return True
```

#### 批量处理
脚本支持处理大量数据，会自动：
- 在请求之间添加延迟
- 处理分页
- 错误重试
- 详细日志记录

### 注意事项
1. 请遵守网站的使用条款
2. 不要过于频繁地请求，避免对服务器造成压力
3. 定期检查页面结构变化，及时更新选择器
4. 建议在非高峰时段运行脚本

### 技术支持
如果遇到问题：
1. 查看 `yanxuan_scraper.log` 日志文件
2. 运行 `demo_scraper.py` 检查页面结构
3. 检查 `config.py` 中的选择器是否需要更新
