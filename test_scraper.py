#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证基本功能
"""

import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def test_chrome_driver():
    """测试Chrome驱动是否正常工作"""
    print("测试Chrome驱动...")
    
    try:
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 测试访问一个简单页面
        driver.get("https://www.baidu.com")
        title = driver.title
        print(f"成功访问页面，标题: {title}")
        
        driver.quit()
        print("Chrome驱动测试成功！")
        return True
        
    except Exception as e:
        print(f"Chrome驱动测试失败: {e}")
        return False

def test_yanxuan_access():
    """测试是否能访问严选页面"""
    print("测试严选页面访问...")
    
    try:
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 尝试访问严选页面
        driver.get("https://yx.mail.netease.com/distribution-operation/admin/#/gallery/list")
        
        print(f"当前URL: {driver.current_url}")
        print(f"页面标题: {driver.title}")
        
        # 检查是否需要登录
        if "login" in driver.current_url.lower():
            print("页面需要登录")
        else:
            print("页面可以直接访问")
        
        driver.quit()
        print("严选页面访问测试完成！")
        return True
        
    except Exception as e:
        print(f"严选页面访问测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("严选爬虫测试工具")
    print("=" * 40)
    
    # 测试Chrome驱动
    if not test_chrome_driver():
        print("Chrome驱动测试失败，请检查Chrome浏览器是否已安装")
        sys.exit(1)
    
    print()
    
    # 测试严选页面访问
    if not test_yanxuan_access():
        print("严选页面访问测试失败")
        sys.exit(1)
    
    print()
    print("所有测试通过！可以运行主脚本了。")

if __name__ == "__main__":
    main()
