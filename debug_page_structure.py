#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试页面结构脚本 - 用于查看页面元素和商品ID位置
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def debug_page_structure():
    """调试页面结构"""
    print("页面结构调试工具")
    print("=" * 40)
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    # 启动浏览器
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # 访问画廊列表页面
        print("1. 访问画廊列表页面...")
        driver.get("https://yx.mail.netease.com/distribution-operation/admin/#/gallery/list")
        
        # 自动登录
        time.sleep(3)
        if "login" in driver.current_url.lower():
            print("2. 自动登录...")
            try:
                username_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text']")))
                username_input.clear()
                username_input.send_keys("zhangxin03")
                
                password_input = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
                password_input.clear()
                password_input.send_keys("XINxinzhang124#")
                
                login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
                login_button.click()
                
                time.sleep(5)
                print("登录完成")
            except Exception as e:
                print(f"自动登录失败: {e}")
                input("请手动登录后按回车继续...")
        
        print("3. 查找并点击第一个tab...")
        tabs = driver.find_elements(By.CSS_SELECTOR, ".ant-tabs-tab")
        if tabs and len(tabs) > 0:
            first_tab = tabs[0]
            print(f"点击tab: {first_tab.text}")
            first_tab.click()
            time.sleep(3)
        
        print("4. 分析页面结构...")
        
        # 查找表格
        tables = driver.find_elements(By.CSS_SELECTOR, "table, .ant-table")
        print(f"找到 {len(tables)} 个表格")
        
        if tables:
            table = tables[0]
            # 查找表头
            headers = table.find_elements(By.CSS_SELECTOR, "th, .ant-table-thead th")
            print("表头信息:")
            for i, header in enumerate(headers):
                print(f"  列 {i+1}: {header.text}")
            
            # 查找表格行
            rows = table.find_elements(By.CSS_SELECTOR, "tbody tr, .ant-table-tbody tr")
            print(f"找到 {len(rows)} 行数据")
            
            if rows:
                print("第一行数据:")
                first_row = rows[0]
                cells = first_row.find_elements(By.CSS_SELECTOR, "td")
                for i, cell in enumerate(cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        print(f"  列 {i+1}: {cell_text}")
                
                print("\n所有行的第2列数据（可能是商品ID）:")
                for i, row in enumerate(rows[:10]):  # 只显示前10行
                    cells = row.find_elements(By.CSS_SELECTOR, "td")
                    if len(cells) > 1:
                        print(f"  行 {i+1}: {cells[1].text.strip()}")
                
                print("\n所有行的第3列数据（可能是商品ID）:")
                for i, row in enumerate(rows[:10]):  # 只显示前10行
                    cells = row.find_elements(By.CSS_SELECTOR, "td")
                    if len(cells) > 2:
                        print(f"  行 {i+1}: {cells[2].text.strip()}")
        
        # 查找所有可能包含ID的元素
        print("\n5. 查找所有可能的商品ID元素...")
        
        # 查找包含数字的元素
        all_elements = driver.find_elements(By.CSS_SELECTOR, "td, span, div")
        potential_ids = []
        
        for element in all_elements:
            text = element.text.strip()
            if text and len(text) > 5 and len(text) < 50:
                # 检查是否包含数字
                if any(char.isdigit() for char in text):
                    potential_ids.append(text)
        
        # 去重并显示
        unique_ids = list(set(potential_ids))
        print(f"找到 {len(unique_ids)} 个可能的ID:")
        for i, id_text in enumerate(unique_ids[:20]):  # 只显示前20个
            print(f"  {i+1}: {id_text}")
        
        print("\n6. 页面HTML片段:")
        # 获取页面HTML的一部分
        page_source = driver.page_source
        # 查找表格部分
        if "<table" in page_source:
            start = page_source.find("<table")
            end = page_source.find("</table>", start) + 8
            table_html = page_source[start:end]
            print(table_html[:2000])  # 只显示前2000个字符
        
        input("\n按回车关闭浏览器...")
        
    except Exception as e:
        print(f"调试过程中发生错误: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_page_structure()
