# 严选运营后台商品ID抓取工具

这个工具用于抓取严选运营后台画廊列表页面的渠道商品ID，并验证这些ID在主图列表页面中的存在性。

## 功能特性

- 自动抓取画廊列表页面所有tab下的商品ID
- 支持翻页抓取，确保获取完整数据
- 自动在主图列表页面搜索验证商品ID
- 记录未找到的商品ID和对应的tab名称
- 支持有头/无头模式运行
- 自动管理Chrome驱动程序
- 详细的日志记录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行脚本：
```bash
python yanxuan_scraper.py
```

2. 根据提示选择是否使用无头模式（建议首次运行选择有头模式以便处理登录）

3. 如果需要登录，脚本会提示您手动登录，登录完成后按回车继续

4. 脚本会自动：
   - 抓取所有tab下的商品ID
   - 在主图列表页面验证每个商品ID
   - 保存未找到的商品ID到CSV和JSON文件

## 输出文件

- `not_found_products_YYYYMMDD_HHMMSS.csv` - CSV格式的未找到商品ID列表
- `not_found_products_YYYYMMDD_HHMMSS.json` - JSON格式的未找到商品ID列表
- `yanxuan_scraper.log` - 详细的运行日志

## 注意事项

1. 首次运行需要下载Chrome驱动，请确保网络连接正常
2. 如果遇到登录问题，建议使用有头模式手动登录
3. 脚本会在请求之间添加延迟，避免对服务器造成过大压力
4. 请确保Chrome浏览器已安装

## 自定义配置

如果需要修改页面元素选择器或其他配置，请编辑 `yanxuan_scraper.py` 文件中的相关部分：

- `extract_product_ids_from_page()` - 修改商品ID提取逻辑
- `search_product_in_picture_list()` - 修改搜索逻辑
- 各种CSS选择器 - 根据实际页面结构调整

## 故障排除

1. **Chrome驱动问题**：脚本会自动下载匹配的Chrome驱动
2. **元素定位失败**：可能需要根据页面更新调整CSS选择器
3. **登录问题**：使用有头模式，手动完成登录流程
4. **网络超时**：检查网络连接，必要时增加等待时间

## 技术实现

- 使用Selenium WebDriver进行浏览器自动化
- 支持动态页面内容抓取
- 智能等待和重试机制
- 完整的错误处理和日志记录
