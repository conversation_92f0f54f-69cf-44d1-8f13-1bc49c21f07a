#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示脚本 - 展示如何使用严选爬虫
这个脚本会打开浏览器让您手动登录，然后演示抓取过程
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def demo_scraper():
    """演示爬虫功能"""
    print("严选爬虫演示")
    print("=" * 40)
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    # 启动浏览器
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # 访问画廊列表页面
        print("1. 访问画廊列表页面...")
        driver.get("https://yx.mail.netease.com/distribution-operation/admin/#/gallery/list")
        
        # 检查是否需要登录
        if "login" in driver.current_url.lower():
            print("2. 需要登录，请在浏览器中完成登录...")
            input("登录完成后按回车继续...")
        
        print("3. 查找页面中的tab...")
        time.sleep(3)
        
        # 查找tab
        tabs = driver.find_elements(By.CSS_SELECTOR, ".ant-tabs-tab")
        if not tabs:
            tabs = driver.find_elements(By.CSS_SELECTOR, "[role='tab']")
        
        if tabs:
            print(f"找到 {len(tabs)} 个tab:")
            for i, tab in enumerate(tabs):
                tab_text = tab.text.strip()
                if tab_text:
                    print(f"  - Tab {i+1}: {tab_text}")
        else:
            print("未找到tab元素，可能需要调整选择器")
        
        print("\n4. 查找页面中的商品ID...")
        
        # 尝试查找商品ID
        product_elements = []
        
        # 尝试多种选择器
        selectors = [
            "td[data-label*='商品ID']",
            "td[data-label*='渠道商品ID']", 
            ".product-id",
            "[class*='product-id']",
            "td:nth-child(2)",
            "td:nth-child(3)",
        ]
        
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                    product_elements = elements
                    break
            except:
                continue
        
        if product_elements:
            print("找到的商品ID示例:")
            for i, element in enumerate(product_elements[:5]):  # 只显示前5个
                text = element.text.strip()
                if text:
                    print(f"  - {text}")
        else:
            print("未找到商品ID元素，可能需要调整选择器")
            print("页面源码片段:")
            print(driver.page_source[:1000])
        
        print("\n5. 测试主图列表页面...")
        driver.get("https://yx.mail.netease.com/distribution-operation/admin/#/picture/primary/list")
        time.sleep(3)
        
        # 查找搜索框
        search_input = None
        search_selectors = [
            "input[placeholder*='搜索']",
            "input[placeholder*='商品']",
            ".ant-input",
            "input[type='text']"
        ]
        
        for selector in search_selectors:
            try:
                search_input = driver.find_element(By.CSS_SELECTOR, selector)
                if search_input:
                    print(f"使用选择器 '{selector}' 找到搜索框")
                    break
            except:
                continue
        
        if search_input:
            print("找到搜索框，可以进行搜索测试")
        else:
            print("未找到搜索框，可能需要调整选择器")
        
        print("\n演示完成！")
        input("按回车关闭浏览器...")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    demo_scraper()
