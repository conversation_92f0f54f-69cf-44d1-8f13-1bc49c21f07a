#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严选运营后台商品ID抓取脚本
抓取画廊列表页面的渠道商品ID，并在主图列表页面验证
"""

import time
import json
import csv
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('yanxuan_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class YanxuanScraper:
    def __init__(self, headless=False):
        """初始化爬虫"""
        self.driver = None
        self.wait = None
        self.headless = headless
        self.gallery_url = "https://yx.mail.netease.com/distribution-operation/admin/#/gallery/list"
        self.picture_url = "https://yx.mail.netease.com/distribution-operation/admin/#/picture/primary/list"
        self.not_found_items = []  # 存储未找到的商品ID和tab名称
        
    def setup_driver(self):
        """设置Chrome驱动"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Chrome驱动初始化成功")
        except Exception as e:
            logger.error(f"Chrome驱动初始化失败: {e}")
            raise
    
    def login_if_needed(self, username="zhangxin03", password="XINxinzhang124#"):
        """检查是否需要登录并自动登录"""
        try:
            # 等待页面加载
            time.sleep(3)

            # 检查是否在登录页面
            if "login" in self.driver.current_url.lower():
                logger.info("检测到登录页面，开始自动登录...")

                # 查找用户名输入框
                username_selectors = [
                    "input[name='username']",
                    "input[name='email']",
                    "input[type='text']",
                    "#username",
                    "#email",
                    ".username-input",
                    ".email-input"
                ]

                username_input = None
                for selector in username_selectors:
                    try:
                        username_input = self.wait.until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                        logger.info(f"找到用户名输入框: {selector}")
                        break
                    except:
                        continue

                if username_input:
                    username_input.clear()
                    username_input.send_keys(username)
                    logger.info("已输入用户名")
                else:
                    logger.error("未找到用户名输入框")
                    return False

                # 查找密码输入框
                password_selectors = [
                    "input[name='password']",
                    "input[type='password']",
                    "#password",
                    ".password-input"
                ]

                password_input = None
                for selector in password_selectors:
                    try:
                        password_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        logger.info(f"找到密码输入框: {selector}")
                        break
                    except:
                        continue

                if password_input:
                    password_input.clear()
                    password_input.send_keys(password)
                    logger.info("已输入密码")
                else:
                    logger.error("未找到密码输入框")
                    return False

                # 查找登录按钮
                login_button_selectors = [
                    "button[type='submit']",
                    "input[type='submit']",
                    ".login-btn",
                    ".submit-btn",
                    "button:contains('登录')",
                    "button:contains('登陆')",
                    "button:contains('Sign')"
                ]

                login_button = None
                for selector in login_button_selectors:
                    try:
                        login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                        logger.info(f"找到登录按钮: {selector}")
                        break
                    except:
                        continue

                if login_button:
                    login_button.click()
                    logger.info("已点击登录按钮")
                else:
                    # 尝试按回车键登录
                    from selenium.webdriver.common.keys import Keys
                    password_input.send_keys(Keys.RETURN)
                    logger.info("已按回车键登录")

                # 等待登录完成
                time.sleep(5)

                # 检查是否登录成功
                if "login" not in self.driver.current_url.lower():
                    logger.info("登录成功！")
                    return True
                else:
                    logger.warning("自动登录可能失败，请检查")
                    return False

        except Exception as e:
            logger.error(f"登录过程失败: {e}")
            return False

    def switch_to_taobao_team(self):
        """切换到淘系团队空间"""
        try:
            logger.info("开始切换到淘系团队空间...")

            # 等待页面加载
            time.sleep(3)

            # 查找团队空间切换按钮或下拉菜单
            team_selectors = [
                ".team-selector",
                ".workspace-selector",
                ".space-selector",
                "[class*='team']",
                "[class*='workspace']",
                "[class*='space']",
                ".ant-select",
                ".dropdown-trigger"
            ]

            team_element = None
            for selector in team_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            team_element = element
                            logger.info(f"找到团队空间选择器: {selector}")
                            break
                    if team_element:
                        break
                except:
                    continue

            if team_element:
                # 点击团队空间选择器
                team_element.click()
                time.sleep(2)

                # 查找"淘系"选项
                taobao_selectors = [
                    "//div[contains(text(), '淘系')]",
                    "//span[contains(text(), '淘系')]",
                    "//li[contains(text(), '淘系')]",
                    "//option[contains(text(), '淘系')]",
                    "[title*='淘系']",
                    "[data-value*='淘系']"
                ]

                taobao_option = None
                for selector in taobao_selectors:
                    try:
                        if selector.startswith("//"):
                            taobao_option = self.driver.find_element(By.XPATH, selector)
                        else:
                            taobao_option = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if taobao_option and taobao_option.is_displayed():
                            logger.info(f"找到淘系选项: {selector}")
                            break
                    except:
                        continue

                if taobao_option:
                    taobao_option.click()
                    logger.info("已切换到淘系团队空间")
                    time.sleep(3)  # 等待页面刷新
                    return True
                else:
                    logger.warning("未找到淘系选项")
                    return False
            else:
                logger.warning("未找到团队空间选择器")
                return False

        except Exception as e:
            logger.error(f"切换团队空间失败: {e}")
            return False
    
    def get_tabs(self):
        """获取所有tab标签"""
        try:
            # 等待tab元素加载
            tabs = self.wait.until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".ant-tabs-tab"))
            )
            
            tab_info = []
            for i, tab in enumerate(tabs):
                tab_text = tab.text.strip()
                if tab_text:
                    tab_info.append({
                        'index': i,
                        'name': tab_text,
                        'element': tab
                    })
                    logger.info(f"发现tab: {tab_text}")
            
            return tab_info
            
        except TimeoutException:
            logger.error("未找到tab元素")
            return []
    
    def extract_product_ids_from_page(self):
        """从当前页面提取渠道商品ID"""
        from config import PRODUCT_ID_SELECTORS, PRODUCT_ID_XPATH_SELECTORS, is_valid_product_id

        product_ids = []
        try:
            # 等待表格加载
            time.sleep(2)

            # 尝试多种CSS选择器
            for selector in PRODUCT_ID_SELECTORS:
                try:
                    id_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if id_elements:
                        logger.debug(f"使用选择器 {selector} 找到 {len(id_elements)} 个元素")
                        break
                except:
                    continue

            # 如果CSS选择器没找到，尝试XPath选择器
            if not id_elements:
                for xpath in PRODUCT_ID_XPATH_SELECTORS:
                    try:
                        id_elements = self.driver.find_elements(By.XPATH, xpath)
                        if id_elements:
                            logger.debug(f"使用XPath {xpath} 找到 {len(id_elements)} 个元素")
                            break
                    except:
                        continue

            # 提取并验证商品ID
            for element in id_elements:
                text = element.text.strip()
                if is_valid_product_id(text):
                    product_ids.append(text)

            # 去重
            product_ids = list(set(product_ids))
            logger.info(f"当前页面提取到 {len(product_ids)} 个有效商品ID")

            if product_ids:
                logger.debug(f"商品ID示例: {product_ids[:3]}")

            return product_ids

        except Exception as e:
            logger.error(f"提取商品ID失败: {e}")
            return []
    
    def has_next_page(self):
        """检查是否有下一页"""
        try:
            next_button = self.driver.find_element(By.CSS_SELECTOR, 
                ".ant-pagination-next:not(.ant-pagination-disabled)")
            return True
        except NoSuchElementException:
            return False
    
    def go_to_next_page(self):
        """翻到下一页"""
        try:
            next_button = self.driver.find_element(By.CSS_SELECTOR, 
                ".ant-pagination-next:not(.ant-pagination-disabled)")
            next_button.click()
            time.sleep(3)  # 等待页面加载
            return True
        except Exception as e:
            logger.error(f"翻页失败: {e}")
            return False
    
    def scrape_tab_products(self, tab_info):
        """抓取指定tab下的所有商品ID"""
        logger.info(f"开始抓取tab: {tab_info['name']}")
        
        # 点击tab
        try:
            tab_info['element'].click()
            time.sleep(2)
        except Exception as e:
            logger.error(f"点击tab失败: {e}")
            return []
        
        all_product_ids = []
        page_num = 1
        
        while True:
            logger.info(f"抓取 {tab_info['name']} - 第{page_num}页")
            
            # 提取当前页面的商品ID
            page_product_ids = self.extract_product_ids_from_page()
            all_product_ids.extend(page_product_ids)
            
            # 检查是否有下一页
            if not self.has_next_page():
                logger.info(f"{tab_info['name']} 已抓取完所有页面")
                break
                
            # 翻页
            if not self.go_to_next_page():
                logger.warning(f"{tab_info['name']} 翻页失败，停止抓取")
                break
                
            page_num += 1
        
        logger.info(f"tab {tab_info['name']} 共抓取到 {len(all_product_ids)} 个商品ID")
        return all_product_ids

    def search_product_in_picture_list(self, product_id):
        """在主图列表页面搜索商品ID"""
        from config import SEARCH_INPUT_SELECTORS, SEARCH_BUTTON_SELECTORS, NO_DATA_SELECTORS, NO_DATA_TEXTS, TABLE_ROW_SELECTORS

        try:
            # 导航到主图列表页面
            self.driver.get(self.picture_url)
            time.sleep(3)

            # 查找搜索框
            search_input = None
            for selector in SEARCH_INPUT_SELECTORS:
                try:
                    search_input = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    logger.debug(f"使用选择器 {selector} 找到搜索框")
                    break
                except:
                    continue

            if not search_input:
                logger.error("未找到搜索框")
                return None

            # 清空搜索框并输入商品ID
            search_input.clear()
            search_input.send_keys(product_id)

            # 查找并点击搜索按钮
            search_button = None
            for selector in SEARCH_BUTTON_SELECTORS:
                try:
                    search_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if search_button:
                        logger.debug(f"使用选择器 {selector} 找到搜索按钮")
                        break
                except:
                    continue

            if search_button:
                search_button.click()
            else:
                # 如果没找到搜索按钮，尝试按回车
                from selenium.webdriver.common.keys import Keys
                search_input.send_keys(Keys.RETURN)

            time.sleep(3)  # 等待搜索结果

            # 检查是否有"暂无数据"提示
            for selector in NO_DATA_SELECTORS:
                try:
                    no_data_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in no_data_elements:
                        element_text = element.text.lower()
                        for no_data_text in NO_DATA_TEXTS:
                            if no_data_text.lower() in element_text:
                                logger.debug(f"找到无数据提示: {element.text}")
                                return False
                except:
                    continue

            # 检查表格是否有数据行
            for selector in TABLE_ROW_SELECTORS:
                try:
                    data_rows = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if data_rows:
                        logger.debug(f"找到 {len(data_rows)} 行数据")
                        return True
                except:
                    continue

            # 如果没有明确的无数据提示，也没有数据行，返回False
            return False

        except Exception as e:
            logger.error(f"搜索商品ID {product_id} 失败: {e}")
            return None

    def scrape_all_tabs(self):
        """抓取所有tab的商品ID"""
        logger.info("开始抓取画廊列表页面")

        # 导航到画廊列表页面
        self.driver.get(self.gallery_url)
        self.login_if_needed()

        # 获取所有tab
        tabs = self.get_tabs()
        if not tabs:
            logger.error("未找到任何tab")
            return

        all_results = {}

        for tab_info in tabs:
            # 重新获取tab元素（避免stale element reference）
            try:
                tabs = self.get_tabs()
                current_tab = next((t for t in tabs if t['name'] == tab_info['name']), None)
                if not current_tab:
                    logger.warning(f"无法重新定位tab: {tab_info['name']}")
                    continue

                product_ids = self.scrape_tab_products(current_tab)
                all_results[tab_info['name']] = product_ids

            except Exception as e:
                logger.error(f"抓取tab {tab_info['name']} 失败: {e}")
                continue

        return all_results

    def verify_products_in_picture_list(self, all_results):
        """验证商品ID在主图列表中是否存在"""
        logger.info("开始验证商品ID在主图列表中的存在性")

        for tab_name, product_ids in all_results.items():
            logger.info(f"验证tab {tab_name} 的 {len(product_ids)} 个商品ID")

            for product_id in product_ids:
                try:
                    search_result = self.search_product_in_picture_list(product_id)

                    if search_result is False:
                        # 未找到该商品ID
                        self.not_found_items.append({
                            'product_id': product_id,
                            'tab_name': tab_name,
                            'timestamp': datetime.now().isoformat()
                        })
                        logger.info(f"商品ID {product_id} 在tab {tab_name} 中未找到")
                    elif search_result is True:
                        logger.debug(f"商品ID {product_id} 在tab {tab_name} 中找到")
                    else:
                        logger.warning(f"商品ID {product_id} 搜索结果不确定")

                except Exception as e:
                    logger.error(f"验证商品ID {product_id} 失败: {e}")
                    continue

                # 添加延迟避免请求过快
                time.sleep(1)

    def save_results(self):
        """保存未找到的商品ID结果"""
        if not self.not_found_items:
            logger.info("所有商品ID都在主图列表中找到了")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存为CSV文件
        csv_filename = f"not_found_products_{timestamp}.csv"
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['product_id', 'tab_name', 'timestamp']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.not_found_items)

        # 保存为JSON文件
        json_filename = f"not_found_products_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.not_found_items, jsonfile, ensure_ascii=False, indent=2)

        logger.info(f"未找到的商品ID已保存到: {csv_filename} 和 {json_filename}")
        logger.info(f"共有 {len(self.not_found_items)} 个商品ID未找到")

    def run(self):
        """运行完整的抓取流程"""
        try:
            self.setup_driver()

            # 抓取所有tab的商品ID
            all_results = self.scrape_all_tabs()

            if not all_results:
                logger.error("未抓取到任何商品ID")
                return

            # 验证商品ID在主图列表中的存在性
            self.verify_products_in_picture_list(all_results)

            # 保存结果
            self.save_results()

        except Exception as e:
            logger.error(f"运行过程中发生错误: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("严选运营后台商品ID抓取工具")
    print("=" * 50)

    headless = input("是否使用无头模式运行？(y/n, 默认n): ").lower().strip() == 'y'

    scraper = YanxuanScraper(headless=headless)
    scraper.run()

if __name__ == "__main__":
    main()
